/* Login Page Styles */
.login-container {
  background: linear-gradient(135deg, #7fc4fd 0%, #a5d8ff 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 450px;
  border: 2px solid #8a2be2;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-title {
  color: #004080;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.login-subtitle {
  color: #666;
  font-size: 1rem;
  margin-bottom: 0;
}

.login-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  font-weight: 600;
  color: #004080;
  margin-bottom: 8px;
  display: block;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #004080;
  box-shadow: 0 0 0 0.25rem rgba(0, 64, 128, 0.25);
}

.btn-login {
  width: 100%;
  background-color: #004080;
  color: white;
  border: none;
  padding: 12px 20px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-login:hover:not(:disabled) {
  background-color: #003366;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 64, 128, 0.3);
}

.btn-login:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}



.alert {
  padding: 12px 15px;
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid transparent;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

/* Responsive Design */
@media (max-width: 576px) {
  .login-card {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}
