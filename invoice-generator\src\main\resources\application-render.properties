# Render Production Configuration
spring.profiles.active=render

# Database Configuration (Ren<PERSON> will provide these via environment variables)
spring.datasource.url=${DATABASE_URL}
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration for PostgreSQL
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false

# Server Configuration
server.port=${PORT:8080}

# CORS Configuration
cors.allowed-origins=${FRONTEND_URL:https://emart-frontend.onrender.com}

# Email Configuration (optional - set via environment variables)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${EMAIL_USERNAME:}
spring.mail.password=${EMAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

# Logging
logging.level.com.emart=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=WARN
