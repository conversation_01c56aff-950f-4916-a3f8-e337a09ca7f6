# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# Environment variables (IMPORTANT: Keep these secret!)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production

# misc
.DS_Store
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo
