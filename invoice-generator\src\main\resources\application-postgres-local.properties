# Local PostgreSQL Configuration for Testing
spring.profiles.active=postgres-local

# PostgreSQL Database Configuration
spring.datasource.url=***********************************************
spring.datasource.username=postgres
spring.datasource.password=admin123
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Server Configuration
server.port=8080

# CORS Configuration (allow frontend)
cors.allowed-origins=http://localhost:3000

# Email Configuration (optional for testing)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your-app-password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Logging
logging.level.com.emart=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate=DEBUG
