# EMART Frontend Environment Template
# Copy this file to .env.local for development or .env.production for production
# Replace the values with your actual configuration

# API URL - Backend server address
REACT_APP_API_URL=http://localhost:8080/api

# For production deployment, use your server IP:
# REACT_APP_API_URL=http://*************:8080/api

# For cloud deployment, use your cloud backend URL:
# REACT_APP_API_URL=https://your-backend.railway.app/api
