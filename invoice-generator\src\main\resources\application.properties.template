# EMART Invoice System Configuration Template
# Copy this file to application.properties and fill in your actual values

# Database Configuration
spring.datasource.url=******************************************
spring.datasource.username=YOUR_DB_USERNAME
spring.datasource.password=YOUR_DB_PASSWORD
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# Server Configuration
server.port=8080
# Allow connections from any IP address (for network deployment)
server.address=0.0.0.0

# Error Handling & Logging
server.error.include-stacktrace=always
server.error.include-message=always
logging.level.com.emart.invoice=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate=DEBUG

# Email Configuration (Gmail with enhanced security)
# IMPORTANT: Use App Password, not your regular Gmail password
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=YOUR_GMAIL_ADDRESS
spring.mail.password=YOUR_GMAIL_APP_PASSWORD
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.protocols=TLSv1.2
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com
spring.mail.properties.mail.smtp.from=YOUR_GMAIL_ADDRESS
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=3000
spring.mail.properties.mail.smtp.writetimeout=5000
spring.mail.default-encoding=UTF-8
