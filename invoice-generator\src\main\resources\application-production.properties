# Production Configuration for Vercel + PlanetScale
spring.profiles.active=production

# PlanetScale Database Configuration (will be set via environment variables)
spring.datasource.url=${DATABASE_URL}
spring.datasource.username=${DATABASE_USERNAME}
spring.datasource.password=${DATABASE_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration for Production
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=false

# Server Configuration
server.port=${PORT:8080}
server.servlet.context-path=/

# CORS Configuration for Production
cors.allowed-origins=${FRONTEND_URL:https://emart-invoice.vercel.app}

# Email Configuration (will be set via environment variables)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${EMAIL_USERNAME}
spring.mail.password=${EMAIL_PASSWORD}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

# Logging Configuration
logging.level.com.emart=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=WARN
