/* Common styles for all pages */
.page-container {
  min-height: 100vh;
  padding-bottom: 30px;
}

.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #004080;
}

.content-card {
  background-color: #a5d8ff;
  border: 2px solid #8a2be2;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
}

/* Table styles */
.table-dark-blue {
  background-color: #004080;
  color: white;
}

.table-dark-blue th {
  font-weight: 500;
}

.table tbody tr:hover {
  background-color: rgba(165, 216, 255, 0.3);
}

/* Form styles */
.form-control:focus {
  border-color: #004080;
  box-shadow: 0 0 0 0.25rem rgba(0, 64, 128, 0.25);
}

.form-label {
  font-weight: 500;
  color: #004080;
}

/* Search input */
.search-input {
  border: 1px solid #004080;
  padding: 10px 15px;
  border-radius: 5px;
}

/* Action buttons in tables */
.btn-group .btn {
  margin: 0 2px;
}

/* Status badges */
.status-badge {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-pending {
  background-color: #ffc107;
  color: #000;
}

.status-completed {
  background-color: #28a745;
  color: #fff;
}

.status-cancelled {
  background-color: #dc3545;
  color: #fff;
}

/* Detail view sections */
.detail-section {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.detail-section h3 {
  font-size: 1.2rem;
  color: #004080;
  margin-bottom: 15px;
  border-bottom: 1px solid #004080;
  padding-bottom: 5px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content-card {
    padding: 15px;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
}
